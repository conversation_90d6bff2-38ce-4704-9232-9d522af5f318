import json
from typing import Any, Dict

import requests

exceptions = {
    "AgenticAI": "agent",
    "LoopNode": "loop",
}


def fulfill_component(
    node_info: dict,
    api_url: str = "https://app-dev.rapidinnovation.dev/api/v1/components",
) -> Dict[str, Any]:
    """
    Generate a complete workflow node structure for the given node name.

    Args:
        node_name (str): Name of the component (e.g., "SelectDataComponent")
        api_url (str): API endpoint URL

    Returns:
        dict: Complete node structure with definition from API
    """
    try:
        # Fetch component definitions from API
        response = requests.get(api_url)
        response.raise_for_status()

        components_data = response.json()
        # with open("post_processing/data retrival/components.json", "r") as f:
        #     components_data = json.load(f)
        # Find the component definition by searching through all categories
        component_def = None
        node_name = node_info["OriginalType"]
        for category, components in components_data.items():
            if node_name in components:
                component_def = components[node_name]
                break

        if not component_def:
            # List available components for user reference
            available_components = []
            for category, components in components_data.items():
                available_components.extend(components.keys())

            error_msg = f"Component '{node_name}' not found in API response.\n"
            error_msg += f"Available components ({len(available_components)}):\n"
            error_msg += "\n".join(
                [f"  - {comp}" for comp in sorted(available_components)[:20]]
            )
            if len(available_components) > 20:
                error_msg += f"\n  ... and {len(available_components) - 20} more"

            return {"error": error_msg}

        # Generate unique ID with timestamp
        unique_id = node_info["node_id"]

        # Create the complete node structure
        node_structure = {
            "id": unique_id,
            "type": "WorkflowNode",
            "position": {"x": 1760, "y": 1960},
            "data": {
                "label": component_def.get("display_name", node_name),
                "type": exceptions.get(node_name, "component"),
                "originalType": node_name,
                "definition": component_def,  # This contains all the API data
                "config": {},
            },
            "width": 208,
            "height": 194,
            "selected": False,
            "dragging": False,
            "style": {"opacity": 1},
        }

        node_structure["position"] = node_info["position"]
        node_structure["data"]["label"] = node_info["label"]
        node_structure["data"]["config"] = node_info.get("parameters", {})
        node_structure["width"] = node_info["dimension"]["width"]
        node_structure["height"] = node_info["dimension"]["height"]
        inputs = node_structure["data"]["definition"]["inputs"]
        for input_ in inputs:
            if input_["value"] and input_["name"] not in node_info.get(
                "parameters", {}
            ):
                node_structure["data"]["config"][input_["name"]] = input_["value"]
        return node_structure

    except requests.exceptions.RequestException as e:
        return {"error": f"API request failed: {str(e)}"}
    except json.JSONDecodeError as e:
        return {"error": f"Failed to parse API response: {str(e)}"}
    except Exception as e:
        return {"error": f"An error occurred: {str(e)}"}


def fulfill_mcp(
    node_info: dict,
    api_detail_url: str = "https://app-dev.rapidinnovation.dev/api/v1/marketplace/mcps/",
) -> Dict[str, Any]:
    """
    Fetches an MCP from the marketplace by ID and constructs a WorkflowNode.
    Handles the detail API structure (object under 'mcp').
    No authentication required.
    """
    mcp_id = node_info["mcp_id"]
    tool_name = node_info["tool_name"]
    node_position = node_info["position"]
    node_config = node_info.get("parameters", {})
    api_detail_url = api_detail_url + mcp_id
    # 1) Fetch MCP detail
    resp = requests.get(
        api_detail_url.format(mcp_id=mcp_id),
        headers={"Content-Type": "application/json"},
    )
    resp.raise_for_status()
    data = resp.json()
    mcp = data.get("mcp") or data.get("data") or data  # <-- flexible extraction
    # with open(f"post_processing/data retrival/mcps/{mcp_id}.json", "r") as f:
    #     data = json.load(f)
    # mcp = data.get("mcp") or data.get("data") or data   # <-- flexible extraction
    if not mcp:
        raise ValueError(f"No MCP with id={mcp_id} found in marketplace API")

    # 2) Defaults
    if node_config is None:
        node_config = {}

    # 3) Inputs from tools (since input_schema is inside tools, not top-level)
    inputs = []
    required = []
    props = {}

    tools = mcp.get("mcp_tools_config", {}).get("tools", [])
    if tools:
        tool = next(tool for tool in tools if tool["name"] == tool_name)
        props = tool.get("input_schema", {}).get("properties", {})
        required = set(tool.get("input_schema", {}).get("required", []))
        if tool.get("output_schema"):
            has_output = True
            outputs_schema = tool.get("output_schema", {}).get("properties", {})
        else:
            has_output = False
            outputs_schema = {}

    for name, schema in props.items():
        input_type = schema.get("type")
        itype = (
            "array"
            if input_type == "array" or "items" in schema
            else input_type or "string"
        )
        inputs.append(
            {
                "name": name,
                "display_name": name[0].upper() + name[1:].replace("_", " "),
                "info": "",
                "input_type": itype,
                "input_types": [itype, "Any"],
                "required": name in required,
                "is_handle": True,
                "is_list": itype == "array",
                "real_time_refresh": False,
                "advanced": False,
                "value": None,
                "options": None,
                "visibility_rules": None,
                "visibility_logic": "OR",
                "validation": {},
            }
        )

    # 4) Outputs
    outputs = []
    if has_output:
        for name, schema in outputs_schema.items():
            outputs.append(
                {
                    "name": name,
                    "display_name": schema.get("title", name),
                    "output_type": schema.get("type", "Any"),
                }
            )
    else:
        outputs.append(
            {"name": "result", "display_name": "Result", "output_type": "Any"}
        )

    # 5) Definition
    definition = {
        "name": mcp_id,
        "display_name": mcp.get("name", mcp_id),
        "description": mcp.get("description", ""),
        "category": mcp.get("category", ""),
        "icon": (mcp.get("logo") or "").split("/")[-1].split(".")[0].capitalize(),
        "beta": False,
        "inputs": inputs,
        "outputs": outputs,
        "is_valid": True,
        # "path": mcp.get("hosted_url", ""),
        "type": "MCP",
        # "env_keys": [],
        # "env_credential_status": "",
        "logo": mcp.get("logo", ""),
        # "oauth_details": {},
        "mcp_info": {
            "server_id": mcp_id,
            "server_path": "",
            "tool_name": tool_name,
            "input_schema": tool.get("input_schema", {}),
            "output_schema": tool.get("output_schema", {}),
        },
    }

    # 6) WorkflowNode
    node = {
        "id": node_info["node_id"],
        "type": "WorkflowNode",
        "position": node_position,
        "data": {
            "label": node_info["label"],
            "type": "mcp",
            "originalType": node_info["OriginalType"],
            "definition": definition,
            "config": {},
            "oauthConnectionState": {},
        },
        "width": 388,
        "height": 519,
        "selected": True,
        "positionAbsolute": node_position,
        "dragging": False,
        "style": {"opacity": 1},
    }
    node["position"] = node_info["position"]
    node["data"]["label"] = node_info["label"]
    node["data"]["config"] = node_info.get("parameters", {})
    node["width"] = node_info["dimension"]["width"]
    node["height"] = node_info["dimension"]["height"]
    if mcp.get("integrations"):
        node["data"]["definition"]["integrations"] = mcp["integrations"]
    inputs = node["data"]["definition"]["inputs"]
    for input_ in inputs:
        if input_["value"] and input_["name"] not in node_info.get("parameters", {}):
            node["data"]["config"][input_["name"]] = input_["value"]
    return node


def fulfill_workflow(
    node_info: dict,
    api_base_url: str = "https://app-dev.rapidinnovation.dev/api/v1/marketplace/workflows/",
) -> Dict[str, Any]:
    workflow_id = node_info["workflow_id"]
    label = node_info["label"]
    node_position = node_info["position"]
    node_config = node_info.get("parameters", {})
    # Construct the API URL using marketplace endpoint
    api_url = f"{api_base_url}{workflow_id}"

    # Simple headers - no authentication required for marketplace
    headers = {"Content-Type": "application/json"}
    try:
        # Make the API request
        response = requests.get(api_url, headers=headers)

        # Handle different error cases
        if response.status_code == 404:
            raise Exception(f"❌ Workflow not found in marketplace: {workflow_id}")
        elif response.status_code == 500:
            raise Exception("❌ Internal server error - try again later")
        elif response.status_code != 200:
            raise Exception(f"❌ API returned status code: {response.status_code}")

        response.raise_for_status()

        # Parse the JSON response
        api_response = response.json()
        # with open(f"post_processing/data retrival/workflows/{workflow_id}.json", "r") as f:
        #     api_response = json.load(f)
        # Check if it's a successful response
        if not api_response:
            raise ValueError("Empty response from marketplace API")

        # Handle different possible response formats from marketplace
        workflow = None
        if isinstance(api_response, dict):
            # Check for common response wrapper formats
            if "workflow" in api_response:
                workflow = api_response["workflow"]
            elif "data" in api_response:
                workflow = api_response["data"]
            elif "result" in api_response:
                workflow = api_response["result"]
            else:
                # Assume the response itself is the workflow data
                workflow = api_response
        else:
            raise ValueError("Unexpected response format from marketplace API")

        if not workflow:
            raise ValueError("No workflow data found in response")

        # Generate unique node ID
        node_id = node_info["node_id"]

        # Extract start nodes and create inputs
        inputs = []
        start_nodes = workflow.get("start_nodes", [])

        for start_node in start_nodes:
            is_handle_value = start_node.get("type") == "handle"

            input_item = {
                "name": start_node.get("field", "input_data"),
                "display_name": start_node.get("field", "Input Data")
                .replace("_", " ")
                .title(),
                "info": f"Input field: {start_node.get('field', 'input_data')}",
                "input_type": "string",
                "required": True,
                "is_handle": is_handle_value,
                "is_list": False,
                "real_time_refresh": False,
                "advanced": False,
                "value": None,
                "options": None,
                "visibility_rules": None,
                "visibility_logic": "OR",
                "requirement_rules": None,
                "requirement_logic": "OR",
                "transition_id": start_node.get("transition_id"),
            }
            inputs.append(input_item)

        # Standard workflow outputs
        outputs = [
            {
                "name": "execution_status",
                "display_name": "Execution Status",
                "output_type": "string",
            },
            {
                "name": "workflow_execution_id",
                "display_name": "Execution ID",
                "output_type": "string",
            },
            {"name": "message", "display_name": "Message", "output_type": "string"},
        ]

        # Create workflow_info structure
        workflow_info = {
            "id": workflow_id,
            "name": workflow.get("name", ""),
            "description": workflow.get("description", ""),
            "workflow_url": workflow.get("workflow_url"),
            "builder_url": workflow.get("builder_url"),
            "start_nodes": start_nodes,
            "owner_id": workflow.get("owner_id"),
            "user_ids": [workflow.get("owner_id")] if workflow.get("owner_id") else [],
            "owner_type": "user",
            "workflow_template_id": None,
            "template_owner_id": None,
            "is_imported": False,
            "version": workflow.get("version", "1.0.0"),
            "visibility": workflow.get("visibility", "private").lower(),
            "category": workflow.get("category"),
            "tags": workflow.get("tags"),
            "status": workflow.get("status", "active"),
            "is_changes_marketplace": False,
            "is_customizable": True,
            "auto_version_on_update": False,
            "created_at": workflow.get("created_at"),
            "updated_at": workflow.get("updated_at"),
            "available_nodes": workflow.get("available_nodes") or [],
            "is_updated": True,
            "source_version_id": workflow.get("source_version_id"),
        }

        # Create the definition structure
        definition = {
            "name": f"workflow-{workflow_id}",
            "display_name": label,
            "description": label,
            "category": "Workflows",
            "icon": "Workflow",
            "beta": False,
            "path": f"workflow.{workflow_id}",
            "inputs": inputs,
            "outputs": outputs,
            "is_valid": True,
            "type": "Workflow",
            "workflow_info": workflow_info,
        }

        # Create the complete node structure
        node = {
            "id": node_id,
            "type": "WorkflowNode",
            "position": node_position,
            "data": {
                "label": label,
                "type": "component",
                "originalType": f"workflow-{workflow_id}",
                "definition": definition,
                "config": node_config,
            },
            "width": 388,
            "height": 326,
            "selected": False,
            "dragging": False,
            "style": {"opacity": 1},
        }
        node["position"] = node_info["position"]
        node["data"]["label"] = node_info["label"]
        node["data"]["config"] = node_info.get("parameters", {})
        node["width"] = node_info["dimension"]["width"]
        node["height"] = node_info["dimension"]["height"]
        return node

    except requests.RequestException as e:
        raise Exception(f"Failed to fetch workflow data from marketplace: {str(e)}")
    except json.JSONDecodeError as e:
        raise Exception(f"Failed to parse API response: {str(e)}")
    except Exception as e:
        raise Exception(f"Error generating workflow node: {str(e)}")


def _return_node_template(node_info: dict) -> dict:

    node_type = node_info["type"]
    node_type = node_type.lower().strip()

    if node_type == "component":
        return fulfill_component(
            node_info=node_info,
        )
    elif node_type == "workflow":
        return fulfill_workflow(
            node_info=node_info,
        )
    elif node_type == "mcp":
        return fulfill_mcp(
            node_info=node_info,
        )
    else:
        raise ValueError(f"Unknown node type: {node_type}")


def _return_edge_template(edge):
    template = {
        "animated": True,
        "style": {"strokeWidth": 2, "zIndex": 5},
        "id": "reactflow__edge-start-nodeflow-MCP_Google_Sheets_add_single_row-1753351326708row",
        "source": "start-node",
        "sourceHandle": "flow",
        "target": "MCP_Google_Sheets_add_single_row-1753351326708",
        "targetHandle": "row",
        "type": "default",
        "selected": False,
    }
    template.update(edge)
    template["id"] = (
        f"reactflow__edge{edge['source']}{edge['sourceHandle']}-{edge['target']}{edge['targetHandle']}"
    )
    return template


def post_processing(workflow: dict) -> str:
    output = {}
    output["nodes"] = []
    output["edges"] = []
    required_parameters = []
    for node in workflow["nodes"]:
        if node["OriginalType"] == "StartNode":
            node["node_id"] = "start-node"
        output["nodes"].append(_return_node_template(node))
        node = output["nodes"][-1]
        inputs = node["data"]["definition"]["inputs"]
        config = node["data"]["config"]
        for in_ in inputs:
            if in_["required"] and in_["name"] not in config and in_["is_handle"]:
                required_parameters.append((node["id"], in_["name"]))
    for edge in workflow["edges"]:
        target = edge["target"]
        target_handle = edge["targetHandle"]
        if (target, target_handle) in required_parameters:
            required_parameters.remove((target, target_handle))
        output["edges"].append(_return_edge_template(edge))
    for target, target_handle in required_parameters:
        edge = {
            "source": "start-node",
            "sourceHandle": "flow",
            "target": target,
            "targetHandle": target_handle,
        }
        output["edges"].append(_return_edge_template(edge))
    return output
